#!/usr/bin/env python3
"""Debug script to isolate memory leak between tests."""

import duckdb
import json
import tempfile
import os
import psutil
import gc
import time

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024

def create_json_file(data, filepath):
    """Helper to create JSON test files."""
    with open(filepath, 'w') as f:
        json.dump(data, f)

def test_boolean_values():
    """Test boolean values (from the problematic test)."""
    print("=== Running boolean values test ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        data = [True, False, True, False]
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Boolean test result: {len(result)} rows")
        
        conn.close()
        
    finally:
        os.unlink(temp_file)

def test_4d_array():
    """Test 4D array (the memory-intensive test)."""
    print("=== Running 4D array test ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        data = [[[[[1, 2]], [[3, 4]]], [[[5, 6]], [[7, 8]]]]]
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"4D array test result: {len(result)} rows")
        
        conn.close()
        
    finally:
        os.unlink(temp_file)

def test_simple_object():
    """Test simple object."""
    print("=== Running simple object test ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        data = {"name": "test", "value": 42}
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Simple object test result: {len(result)} rows")
        
        conn.close()
        
    finally:
        os.unlink(temp_file)

def run_memory_test():
    """Run tests in different orders to identify memory leak."""
    
    print("Starting memory leak investigation...")
    print(f"Initial memory: {get_memory_usage():.1f} MB")
    
    # Test 1: Run 4D array alone
    print("\n--- Test 1: 4D array alone ---")
    mem_before = get_memory_usage()
    test_4d_array()
    gc.collect()
    time.sleep(0.1)
    mem_after = get_memory_usage()
    print(f"Memory before: {mem_before:.1f} MB, after: {mem_after:.1f} MB, delta: {mem_after - mem_before:.1f} MB")
    
    # Test 2: Run boolean test, then 4D array
    print("\n--- Test 2: Boolean test, then 4D array ---")
    mem_before = get_memory_usage()
    test_boolean_values()
    gc.collect()
    time.sleep(0.1)
    mem_mid = get_memory_usage()
    print(f"Memory after boolean test: {mem_mid:.1f} MB, delta: {mem_mid - mem_before:.1f} MB")
    
    test_4d_array()
    gc.collect()
    time.sleep(0.1)
    mem_after = get_memory_usage()
    print(f"Memory after 4D array: {mem_after:.1f} MB, delta: {mem_after - mem_mid:.1f} MB")
    print(f"Total delta: {mem_after - mem_before:.1f} MB")
    
    # Test 3: Run simple object, then 4D array
    print("\n--- Test 3: Simple object, then 4D array ---")
    mem_before = get_memory_usage()
    test_simple_object()
    gc.collect()
    time.sleep(0.1)
    mem_mid = get_memory_usage()
    print(f"Memory after simple object: {mem_mid:.1f} MB, delta: {mem_mid - mem_before:.1f} MB")
    
    test_4d_array()
    gc.collect()
    time.sleep(0.1)
    mem_after = get_memory_usage()
    print(f"Memory after 4D array: {mem_after:.1f} MB, delta: {mem_after - mem_mid:.1f} MB")
    print(f"Total delta: {mem_after - mem_before:.1f} MB")
    
    # Test 4: Multiple boolean tests
    print("\n--- Test 4: Multiple boolean tests ---")
    mem_before = get_memory_usage()
    for i in range(3):
        print(f"Boolean test iteration {i+1}")
        test_boolean_values()
        gc.collect()
        time.sleep(0.1)
        mem_current = get_memory_usage()
        print(f"Memory: {mem_current:.1f} MB, delta: {mem_current - mem_before:.1f} MB")

if __name__ == "__main__":
    run_memory_test()
